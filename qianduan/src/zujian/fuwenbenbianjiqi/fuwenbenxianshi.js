import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';

// 富文本显示容器
const Fu<PERSON><PERSON><PERSON> = styled.div`
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  line-height: ${props => props.theme.ziti.xinggao.kuansong};
  
  // 基础文本样式
  strong, b {
    font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
    color: ${props => props.theme.yanse.wenzi_zhuyao};
  }

  em, i {
    font-style: italic;
    color: ${props => props.theme.yanse.wenzi_ciyao};
  }

  u {
    text-decoration: underline;
    text-decoration-color: ${props => props.theme.yanse.zhuyao};
    text-underline-offset: 2px;
  }

  // 链接样式
  a {
    color: ${props => props.theme.yanse.zhuyao};
    text-decoration: none;
    border-bottom: 1px solid ${props => props.theme.yanse.zhuyao + '60'};
    transition: all ${props => props.theme.donghua.sujian.kuai};
    padding: 1px 2px;
    border-radius: 2px;

    &:hover {
      border-bottom-color: ${props => props.theme.yanse.zhuyao};
      background: ${props => props.theme.yanse.zhuyao + '10'};
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }
  }

  // 列表样式
  ul, ol {
    margin: ${props => props.theme.jianju.xiao} 0;
    padding-left: ${props => props.theme.jianju.zhongdeng};
  }

  ul {
    list-style: none;
    
    li {
      position: relative;
      margin: ${props => props.theme.jianju.xiaoxiao} 0;
      
      &::before {
        content: '•';
        color: ${props => props.theme.yanse.zhuyao};
        font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
        position: absolute;
        left: -${props => props.theme.jianju.xiao};
        top: 0;
      }
    }
  }

  ol {
    counter-reset: list-counter;
    
    li {
      position: relative;
      margin: ${props => props.theme.jianju.xiaoxiao} 0;
      counter-increment: list-counter;
      
      &::before {
        content: counter(list-counter) '.';
        color: ${props => props.theme.yanse.zhuyao};
        font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
        position: absolute;
        left: -${props => props.theme.jianju.zhongdeng};
        top: 0;
        width: ${props => props.theme.jianju.xiao};
        text-align: right;
      }
    }
  }

  // 段落样式
  p {
    margin: ${props => props.theme.jianju.xiaoxiao} 0;
    text-align: justify;
    
    &:first-child {
      margin-top: 0;
    }
    
    &:last-child {
      margin-bottom: 0;
    }
    
    &:empty {
      margin: 0;
      height: 0;
    }
  }

  // 引用样式
  blockquote {
    margin: ${props => props.theme.jianju.xiao} 0;
    padding: ${props => props.theme.jianju.xiao};
    border-left: 3px solid ${props => props.theme.yanse.zhuyao};
    background: ${props => props.theme.mingcheng === 'anhei'
      ? 'rgba(255, 255, 255, 0.03)'
      : 'rgba(0, 0, 0, 0.03)'};
    border-radius: 0 ${props => props.theme.yuanjiao.xiao} ${props => props.theme.yuanjiao.xiao} 0;
    font-style: italic;
    color: ${props => props.theme.yanse.wenzi_ciyao};
    position: relative;
    
    &::before {
      content: '"';
      font-size: ${props => props.theme.ziti.daxiao.dada};
      color: ${props => props.theme.yanse.zhuyao + '60'};
      position: absolute;
      top: -5px;
      left: ${props => props.theme.jianju.xiaoxiao};
      font-family: serif;
    }
    
    p {
      margin: 0;
    }
  }

  // 分割线样式
  hr {
    border: none;
    height: 1px;
    background: linear-gradient(90deg, 
      transparent 0%, 
      ${props => props.theme.yanse.biankuang} 50%, 
      transparent 100%
    );
    margin: ${props => props.theme.jianju.zhongdeng} 0;
  }

  // 代码样式
  code {
    background: ${props => props.theme.mingcheng === 'anhei'
      ? 'rgba(255, 255, 255, 0.1)'
      : 'rgba(0, 0, 0, 0.1)'};
    padding: 2px 4px;
    border-radius: ${props => props.theme.yuanjiao.xiaoxiao};
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9em;
    color: ${props => props.theme.yanse.zhuyao};
  }

  pre {
    background: ${props => props.theme.mingcheng === 'anhei'
      ? 'rgba(255, 255, 255, 0.05)'
      : 'rgba(0, 0, 0, 0.05)'};
    padding: ${props => props.theme.jianju.xiao};
    border-radius: ${props => props.theme.yuanjiao.xiao};
    overflow-x: auto;
    margin: ${props => props.theme.jianju.xiao} 0;
    
    code {
      background: none;
      padding: 0;
    }
  }

  // 表格样式
  table {
    width: 100%;
    border-collapse: collapse;
    margin: ${props => props.theme.jianju.xiao} 0;
    
    th, td {
      padding: ${props => props.theme.jianju.xiaoxiao};
      border: 1px solid ${props => props.theme.yanse.biankuang};
      text-align: left;
    }
    
    th {
      background: ${props => props.theme.mingcheng === 'anhei'
        ? 'rgba(255, 255, 255, 0.05)'
        : 'rgba(0, 0, 0, 0.05)'};
      font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
      color: ${props => props.theme.yanse.wenzi_zhuyao};
    }
  }

  // 图片样式
  img {
    max-width: 100%;
    height: auto;
    border-radius: ${props => props.theme.yuanjiao.xiao};
    margin: ${props => props.theme.jianju.xiaoxiao} 0;
    box-shadow: ${props => props.theme.mingcheng === 'anhei'
      ? '0 4px 12px rgba(0, 0, 0, 0.3)'
      : '0 4px 12px rgba(0, 0, 0, 0.1)'};
  }

  // 响应式设计
  @media (max-width: 768px) {
    font-size: ${props => props.theme.ziti.daxiao.xiao};
    line-height: ${props => props.theme.ziti.xinggao.putong};
    
    ul, ol {
      padding-left: ${props => props.theme.jianju.xiao};
    }
    
    blockquote {
      padding: ${props => props.theme.jianju.xiaoxiao};
      margin: ${props => props.theme.jianju.xiaoxiao} 0;
    }
  }
`;

// 富文本显示组件
function Fuwenbenxianshi({ 
  neirong = '', 
  donghua = true,
  className = '',
  ...qitacanshu 
}) {
  // 清理和安全处理HTML内容
  const qinglineirong = (html) => {
    if (!html) return '';
    
    // 基础的HTML清理，移除危险标签
    const weixianbiaoqian = /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi;
    const qinglihou = html.replace(weixianbiaoqian, '');
    
    return qinglihou;
  };

  const donghuapeizhi = {
    initial: { opacity: 0, y: 10 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.3, ease: "easeOut" }
  };

  const Rongqi = donghua ? motion.div : 'div';
  const donghuashuxing = donghua ? donghuapeizhi : {};

  return (
    <Rongqi {...donghuashuxing}>
      <Fuwenbenrongqi
        className={className}
        dangerouslySetInnerHTML={{ __html: qinglineirong(neirong) }}
        {...qitacanshu}
      />
    </Rongqi>
  );
}

export default Fuwenbenxianshi;
